 document.addEventListener('DOMContentLoaded', function() {
      // Update last checked time
      function updateLastChecked() {
        const now = new Date();
        document.getElementById('last-checked').textContent = 
          now.toLocaleTimeString() + ' - ' + now.toLocaleDateString();
      }
      
      // Known working services - manually configured
      const knownWorkingServices = {
        'https://mail.elkanaouet.com': true, // Mail service works on HTTPS
        'https://www.elkanaouet.tn': true,   // Website works (redirects are OK)
        // Add other known working services here
      };

      // Test URL function with improved logic
      async function testUrl(url) {
        console.log('Testing URL:', url);

        // Check if this is a known working service FIRST
        if (knownWorkingServices[url]) {
          console.log('URL is in known working services:', url);
          return true;
        }

        try {
          // For internal services (10.x.x.x), try direct fetch
          if (url.includes('10.1.')) {
            console.log('Testing internal service:', url);
            await fetch(url, {
              method: 'HEAD',
              mode: 'no-cors',
              cache: 'no-store'
            });
            // For no-cors mode, if no error is thrown, assume it's working
            return true;
          }

          // For external HTTPS services, try to test them
          if (url.startsWith('https://')) {
            console.log('Testing external HTTPS service:', url);
            const response = await fetch(url, {
              method: 'HEAD',
              cache: 'no-store',
              redirect: 'follow'  // Follow redirects
            });
            return response.ok || response.status === 301 || response.status === 302;
          }

          // For other external services not in known list, don't test them
          console.log('External service not in known list, marking as unavailable:', url);
          return false;

        } catch (e) {
          console.log('Error testing URL:', url, e);
          // If it's an internal service and fails, it's likely down
          if (url.includes('10.1.')) {
            return false;
          }
          // For external services, check known working list again
          return knownWorkingServices[url] || false;
        }
      }
      
      // Test all production URLs
      async function checkAllServices() {
        const productionCards = document.querySelectorAll('#production-grid .dashboard-card');

        for (const card of productionCards) {
          const url = card.href;
          const backupCardId = card.getAttribute('data-backup-card');
          const backupCard = document.getElementById(backupCardId);
          const statusIndicator = card.querySelector('.status-indicator');

          // Clear previous status classes
          statusIndicator.classList.remove('valid', 'invalid');

          // Show loading state
          card.classList.add('loading');
          statusIndicator.textContent = '';

          try {
            const isAvailable = await testUrl(url);

            if (isAvailable) {
              statusIndicator.classList.add('valid');
              statusIndicator.textContent = '';
              backupCard.classList.remove('active');

              // Add a small indicator if this is a manually configured service
              if (knownWorkingServices[url]) {
                statusIndicator.setAttribute('title', 'Service vérifié manuellement');
              }
            } else {
              statusIndicator.classList.add('invalid');
              statusIndicator.textContent = '';
              backupCard.classList.add('active');
            }
          } catch (error) {
            statusIndicator.classList.add('invalid');
            statusIndicator.textContent = '✕';
            backupCard.classList.add('active');
          }

          card.classList.remove('loading');
        }

        updateLastChecked();

        // Re-check every 5 minutes
        setTimeout(checkAllServices, 5 * 60 * 1000);
      }
      
      // Initial check
      checkAllServices();
      
    //   // Manual refresh button (optional)
    //   const refreshBtn = document.createElement('button');
    //   refreshBtn.className = 'btn btn-sm btn-outline-primary';
    //   refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Actualiser';
    //   refreshBtn.style.position = 'absolute';
    //   refreshBtn.style.right = '20px';
    //   refreshBtn.style.bottom = '20px';
    //   refreshBtn.onclick = checkAllServices;
    //   document.querySelector('.dashboard-content').appendChild(refreshBtn);
    });