:root {
  --el-primary: #2e8b57; /* Original lighter green */
  --el-primary-light: #1f9755; /* Brighter green */
  --el-primary-dark: #228b22; /* Forest green */
  --el-secondary: #f8f9fa;
  --el-accent: #ffd700; /* Gold accent */
  --el-text: #ffffff; 
  --el-light: #ffffff;
  --el-dark: #1e5631;
  --el-border: rgba(255, 255, 255, 0.15);
  --el-header-bg: linear-gradient(135deg, var(--el-primary) 0%, var(--el-primary-light) 100%);
  --el-container-bg: rgba(255, 255, 255, 0.96);
  --el-container-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --el-success: #4CAF50; /* Brighter green */
  --el-danger: #FF5252; /* Brighter red */
  --el-card-bg: linear-gradient(145deg, var(--el-primary-light) 0%, var(--el-primary) 100%);
}

/* Base Styles */
body {
  font-family: 'Montserrat', sans-serif;
  background-color: #f0f5f0; /* Light greenish background */
  min-height: 100vh;
  margin: 0;
   background-image: 
    var(--el-bg-gradient),
    url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100" fill="none" stroke="%23c8e6c9" stroke-width="0.5" stroke-opacity="0.3"><rect width="100" height="100" fill="none"/><path d="M0 0 L100 100 M100 0 L0 100" /></svg>');
  background-attachment: fixed;
}

/* Layout Container */
.dashboard-master-container {
  background: var(--el-container-bg);
  box-shadow: var(--el-container-shadow);
  border-radius: 16px;
  margin: 2rem auto;
  max-width: 1400px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Header Styles */
.dashboard-header {
  background: var(--el-header-bg);
  box-shadow: 0 4px 20px rgba(0,0,0,0.2);
  padding: 2.5rem 3rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  color: white;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  inset: 0;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100" fill="none" stroke="%23ffffff" stroke-width="0.3" stroke-opacity="0.15"><rect width="100" height="100" fill="none"/><path d="M0 0 L100 100 M100 0 L0 100" /></svg>');
  opacity: 0.6;
}

.dashboard-title-container {
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.dashboard-title {
  color: var(--el-light);
  font-weight: 700;
  margin: 0;
  font-size: clamp(1.5rem, 4vw, 2.2rem);
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  letter-spacing: -0.5px;
}

.dashboard-subtitle {
  color: rgba(255,255,255,0.9);
  font-weight: 400;
  font-size: clamp(0.8rem, 2vw, 1rem);
  margin-top: 0.5rem;
  max-width: 600px;
  letter-spacing: 0.5px;
}

.company-logo {
  height: clamp(50px, 6vw, 70px);
  transition: transform 0.3s ease, filter 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
  z-index: 2;
}

.company-logo:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 3px 6px rgba(0,0,0,0.3));
}

/* Main Content */
.dashboard-content {
  padding: clamp(1.5rem, 4vw, 3rem);
}

/* Grid System */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1.8rem;
}

/* Perfect Card Styles with Enhanced Gradient */
.dashboard-card {
  background: linear-gradient(145deg, 
              rgba(46, 139, 87, 0.9) 0%, 
              rgba(60, 179, 113, 0.95) 50%, 
              rgba(46, 139, 87, 0.9) 100%);
  border-radius: 12px;
  padding: 1.8rem 1.5rem;
  text-align: center;
  box-shadow: 0 6px 20px rgba(46, 139, 87, 0.2),
              inset 0 1px 1px rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  text-decoration: none;
  color: var(--el-text);
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 180px;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 1;
}

.dashboard-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 75% 30%, 
              rgba(255, 255, 255, 0.15) 0%, 
              transparent 50%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(46, 139, 87, 0.3),
              inset 0 1px 2px rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.dashboard-card:hover::after {
  opacity: 1;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, 
              var(--el-accent) 0%, 
              rgba(255, 215, 0, 0.7) 100%);
  transition: all 0.3s ease;
  z-index: 2;
}

.dashboard-card:hover::before {
  height: 6px;
  background: linear-gradient(90deg, 
              var(--el-accent) 0%, 
              rgba(255, 255, 255, 0.8) 100%);
}
/* Card Elements */
.card-icon {
  font-size: 2.4rem;
  margin-bottom: 1rem;
  color: var(--el-light);
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.dashboard-card:hover .card-icon {
  transform: scale(1.15);
  color: var(--el-accent);
}

.card-main-text {
  font-weight: 700; /* Bolder for better visibility */
  font-size: 1.2rem;
  margin: 0.5rem 0;
  color: var(--el-light);
  width: 100%;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 0.5rem;
  letter-spacing: 0.5px;
}

/* Enhanced Sub-text with Dashes */
.card-sub-text {
  text-align: left;
  width: 100%;
  padding-left: 1.5rem;
  position: relative;
  margin: 0.4rem 0;
  font-weight: 500;
  font-size: 0.9rem; /* Slightly larger for readability */
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.card-sub-text::before {
  content: "•";
  position: absolute;
  left: 0.5rem;
  color: var(--el-accent);
  font-weight: bold;
}

.card-sub-items {
  width: 100%;
  margin: 0.5rem 0;
}

.card-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: var(--el-dark);
  color: var(--el-accent);
  font-size: 0.7rem;
  font-weight: 700;
  padding: 0.25rem 0.6rem;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Enhanced Status Indicators - Make them POP! */
.status-indicator {
  position: absolute;
  top: 12px;
  left: 12px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: grid;
  place-items: center;
  font-size: 14px;
  font-weight: 900;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  border: 2px solid rgba(255,255,255,0.3);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.status-indicator.valid {
  background: var(--el-success);
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  border-color: rgba(255,255,255,0.5);
  animation: pulse-success 1.5s infinite;
}

.status-indicator.invalid {
  background: var(--el-danger);
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  border-color: rgba(255,255,255,0.5);
  animation: pulse-error 1.5s infinite;
}

/* Add icons for status indicators */
.status-indicator.valid::before {
  content: "✓";
  font-size: 16px;
}

.status-indicator.invalid::before {
  content: "✕";
  font-size: 16px;
}

/* Animation for status indicators */
@keyframes pulse-success {
  0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
  100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes pulse-error {
  0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
  100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

/* Hover effects */
.dashboard-card:hover .status-indicator {
  transform: scale(1.1);
}

.dashboard-card:hover .status-indicator.valid {
  background: #28e076;
  animation: none;
  box-shadow: 0 0 0 5px rgba(40, 224, 118, 0.4);
}

.dashboard-card:hover .status-indicator.invalid {
  background: #ff3a4a;
  animation: none;
  box-shadow: 0 0 0 5px rgba(255, 58, 74, 0.4);
}



/* Backup Cards */
.backup-card {
  border: 2px dashed rgba(255, 255, 255, 0.4) !important;
  background-color: rgba(26, 93, 62, 0.3) !important;
  opacity: 0.8;
  pointer-events: none;
  filter: grayscale(50%);
}

.backup-card::before {
  background: var(--el-secondary) !important;
  height: 3px !important;
}

.backup-card .card-icon {
  color: rgba(255, 255, 255, 0.6) !important;
}

.backup-card:hover .card-icon {
  color: var(--el-accent) !important;
}

.backup-badge {
  background: var(--el-dark) !important;
  color: var(--el-accent) !important;
}

.backup-card::after {
  content: "BACKUP";
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.3);
  font-weight: bold;
  letter-spacing: 1px;
}

.backup-card.active {
  opacity: 1;
  pointer-events: auto;
  filter: grayscale(0%);
  background-color: rgba(26, 93, 62, 0.6);
  border-left: 4px solid var(--el-accent) !important;
  animation: pulse 2s infinite;
}

/* Footer */
.dashboard-footer {
  margin-top: 3rem;
  text-align: center;
  color: var(--el-primary-dark);
  font-size: 0.85rem;
  padding: 1.5rem 0;
  border-top: 1px solid rgba(26, 93, 62, 0.2);
  background: rgba(255, 255, 255, 0.9);
}

/* Section Titles */
.section-title {
  color: var(--el-primary-dark);
  font-weight: 700;
  font-size: 1.4rem;
  margin: 2.5rem 0 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(26, 93, 62, 0.2);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title:first-child {
  margin-top: 0;
}

/* Special Icon Styling */
.card-icon.fa-medal {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Animations */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Loading State */
.dashboard-card.loading .status-indicator::after {
  content: "↻";
  animation: spin 1s linear infinite;
  color: var(--el-primary);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(26, 93, 62, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  color: white;
}

.spinner {
  font-size: 1.2rem;
  color: var(--el-light);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 2rem 1.5rem;
  }
  
  .section-title {
    font-size: 1.2rem;
    margin: 2rem 0 1.2rem;
  }
}

@media (max-width: 576px) {
  .dashboard-master-container {
    margin: 1rem;
    border-radius: 12px;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.2rem;
  }
  
  .dashboard-card {
    padding: 1.2rem 0.8rem;
    min-height: 150px;
  }
  
  .card-icon {
    font-size: 2rem;
  }
  
  .card-main-text {
    font-size: 1rem;
  }
  
  .card-sub-text {
    font-size: 0.8rem;
    padding-left: 1.2rem;
  }
  
  .card-sub-text::before {
    left: 0.3rem;
  }
}

/* Card Visibility Rules */
.dashboard-card:empty,
.dashboard-card:not(:has(.card-main-text)) {
  display: none;
}

.dashboard-card:has(.card-main-text):not(:has(.card-sub-text)) {
  justify-content: center;
  min-height: 140px;
}

.dashboard-card:has(.card-main-text):not(:has(.card-sub-text)) .card-icon {
  margin-bottom: 0.5rem;
}

.dashboard-card:has(.card-main-text):has(.card-sub-text:only-child) {
  justify-content: center;
  text-align: center;
}

.dashboard-card:has(.card-main-text):has(.card-sub-text:only-child) .card-sub-text {
  text-align: center;
  padding-left: 0;
}

.dashboard-card:has(.card-main-text):has(.card-sub-text:only-child) .card-sub-text::before {
  content: none;
}

.dashboard-grid {
  grid-auto-flow: dense;
}