# SI Dashboard - El Kanaouet

![image](https://github.com/user-attachments/assets/a58ca1a2-0f81-4c6a-822c-bb09d25555c4) , ![image](https://github.com/user-attachments/assets/255f7b35-f647-49e5-99de-cba82f9af3f1)



A responsive web dashboard for monitoring El Kanaouet's information systems, with automatic failover detection to backup systems.

## Features

- **Real-time monitoring** of production systems
- **Automatic failover detection** with visual indicators (✅/❌)
- **Backup system activation** when primary systems are down
- **Responsive design** works on all devices
- **Automatic refresh** every 5 minutes
- **Manual refresh** button
- **Last checked** timestamp

## Technologies Used

- HTML5
- CSS3 (with CSS Variables)
- JavaScript (ES6)
- [Bootstrap 5](https://getbootstrap.com/)
- [Font Awesome](https://fontawesome.com/) icons
- [Google Fonts](https://fonts.google.com/) (Montserrat)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/si-dashboard.git
